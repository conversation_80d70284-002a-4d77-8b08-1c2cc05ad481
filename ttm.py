# 拉取数据
df = pro.cashflow(**{
    "ts_code": "000651.sz",
    "ann_date": "",
    "f_ann_date": "",
    "start_date": "",
    "end_date": "",
    "period": "",
    "report_type": "",
    "comp_type": 1,
    "is_calc": "",
    "limit": "",
    "offset": ""
}, fields=[
    "ts_code",
    "end_date",
    "net_profit",
    "n_cashflow_act"
])
print(df)
# 拉取数据
df = pro.daily_basic(**{
    "ts_code": "000651.sz",
    "trade_date": "",
    "start_date": "",
    "end_date": "",
    "limit": "",
    "offset": ""
}, fields=[
    "ts_code",
    "trade_date",
    "circ_mv",
    "total_mv"
])
print(df)

# 拉取数据
df = pro.moneyflow(**{
    "ts_code": "000651.sz",
    "trade_date": "",
    "start_date": "",
    "end_date": "",
    "limit": "",
    "offset": ""
}, fields=[
    "ts_code",
    "trade_date",
    "buy_sm_vol",
    "buy_sm_amount",
    "sell_sm_vol",
    "sell_sm_amount",
    "buy_md_vol",
    "buy_md_amount",
    "sell_md_vol",
    "sell_md_amount",
    "buy_lg_vol",
    "buy_lg_amount",
    "sell_lg_vol",
    "sell_lg_amount",
    "buy_elg_vol",
    "buy_elg_amount",
    "sell_elg_vol",
    "sell_elg_amount",
    "net_mf_vol",
    "net_mf_amount",
    "trade_count"
])
print(df)
